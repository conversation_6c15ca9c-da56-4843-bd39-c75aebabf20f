package com.cmc.riskcontrol.business.aws;

import com.cmc.framework.s3.CmcS3Client;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class AwsBeanConfig {

    @Primary
    @Bean
    public AwsS3Service awsS3Service(AwsS3Config config, CmcS3Client cmcS3Client) {
        return new AwsS3Service(config, cmcS3Client);
    }

    @Primary
    @Bean
    @ConfigurationProperties(prefix = "com.cmc.riskcontrol.aws.s3")
    public AwsS3Config s3Config() {
        return new AwsS3Config();
    }
}