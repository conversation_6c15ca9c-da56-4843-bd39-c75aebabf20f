package com.cmc.riskcontrol.business.aws;

import com.cmc.framework.s3.CmcS3Client;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

/**
 * Service for accessing AWS S3.
 *
 * <AUTHOR>
 * @date 2020/12/24
 */
@Slf4j
public class AwsS3Service {

    private final AwsS3Config config;

    private final CmcS3Client cmcS3Client;

    public AwsS3Service(AwsS3Config config, CmcS3Client cmcS3Client) {
        this.config = config;
        this.cmcS3Client = cmcS3Client;
    }

    public Mono<PutObjectResponse> uploadToS3ByBytes(String targetPath, byte[] file,String tag) {
        PutObjectRequest request = PutObjectRequest.builder()
            .key(targetPath)
            .bucket(config.getBucket())
            .contentType("application/gzip")
            .build();
       return cmcS3Client.putObject(request, AsyncRequestBody.fromBytes(file),tag);
    }

    /**
     * download file to memory stream
     * @param sourcePath
     * @return
     * @throws IOException
     */
    public Mono<InputStream> downloadLargeFileToMemory(String sourcePath,String tag) {
        GetObjectRequest request = GetObjectRequest.builder()
            .bucket(config.getBucket())
            .key(sourcePath)
            .build();

        return cmcS3Client.getObjectAsBytes(request,tag).map(responseBytes -> {
            byte[] byteArray = responseBytes.asByteArray();
            return new ByteArrayInputStream(byteArray);
        });
    }

}
