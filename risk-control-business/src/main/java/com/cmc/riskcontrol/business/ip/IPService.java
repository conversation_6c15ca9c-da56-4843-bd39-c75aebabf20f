package com.cmc.riskcontrol.business.ip;

import static com.cmc.riskcontrol.model.constant.Constants.S3_MMDB_FILE_TAG;

import com.cmc.riskcontrol.business.aws.AwsS3Service;
import com.cmc.riskcontrol.business.utils.RateLimiter;
import com.cmc.riskcontrol.model.dto.IPAddressDto;
import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.maxmind.geoip2.record.Subdivision;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @since 2022/2/10 11:19
 */
@Component
@Slf4j
public class IPService implements InitializingBean {

    @Autowired
    private AwsS3Service awsS3Service;

    @Autowired
    private ResourceLoader resourceLoader;

    @Value("${com.cmc.riskcontrol.file.localPath:/tmp}")
    private String localPath;

    @Value("${com.cmc.riskcontrol.file.mmdb.s3-path}")
    private String s3Path;

    @Value("${com.cmc.riskcontrol.file.mmdb.refresh-interval:168}")
    private Long refreshInterval;

    @Value("${com.cmc.riskcontrol.file.mmdb.max-requests:1}")
    private Integer maxRequests;

    @Value("${com.cmc.riskcontrol.file.mmdb.request-interval-ms:300000}")
    private Integer requestInterval;

    private static DatabaseReader reader = null;

    private static RateLimiter rateLimiter = null;

    public static final String MMDB_FILENAME = "GeoIP2-City.mmdb";

    public IPAddressDto parse(String ip) {
        try {
            InetAddress ipAddress = InetAddress.getByName(ip);
            CityResponse response = reader.city(ipAddress);
            Country country = response.getCountry();
            Subdivision subdivision = response.getMostSpecificSubdivision();
            City city = response.getCity();

            return IPAddressDto.builder()
                               .ip(ip)
                               .ipCountry(country.getIsoCode())
                               .ipRegion(subdivision.getName())
                               .ipCity(city.getName())
                               .build();
        } catch (Exception e) {
            return IPAddressDto.builder().ip(ip).build();
        }
    }

    /**
     * mmdb files have a minimum size of 131 MB and
     * require a download rate limit
     * @return
     */
    public Flux<DataBuffer> getGeoData() {
        if (!rateLimiter.allowRequest()) {
            return Flux.empty();
        }

        int chunkSize = 4096;
        byte[] data = new byte[chunkSize];
        DataBufferFactory bufferFactory = new DefaultDataBufferFactory();
        Resource resource = resourceLoader.getResource("classpath:mmdb/GeoIP2-City.mmdb");
        return Flux.create(sink -> {
            try (InputStream inputStream = resource.getInputStream()) {
                int bytesRead = 0;
                while ((bytesRead = inputStream.read(data)) != -1) {
                    sink.next(bufferFactory.wrap(Arrays.copyOf(data, bytesRead)));
                }
            } catch (IOException e) {
                sink.error(e);
            } finally {
                sink.complete();
            }
        });
    }

    public void refreshRateLimit() {
        rateLimiter.refresh(maxRequests, requestInterval);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        rateLimiter = new RateLimiter(maxRequests, requestInterval);
        refresh();
        updateMmdbFileScheduled();
    }

    private void refresh() {
        try {
            File file = downloadFileFromS3();
            if (file.length() == 0) {
                Resource resource = resourceLoader.getResource("classpath:mmdb/GeoIP2-City.mmdb");
                reader = new DatabaseReader.Builder(resource.getInputStream()).withCache(new CHMCache()).build();
            } else {
                reader = new DatabaseReader.Builder(file).withCache(new CHMCache()).build();
            }
        } catch (IOException e) {
            log.error("refresh fail", e);
        }
    }

    private void updateMmdbFileScheduled() {
        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r);
            t.setName("update-mmdb-thread");
            t.setDaemon(true);
            return t;
        });
        service.scheduleWithFixedDelay(this::refresh, 1, refreshInterval, TimeUnit.HOURS);
    }

    private File downloadFileFromS3() throws IOException {
        File file = new File(String.join("/", localPath, MMDB_FILENAME));
        if (file.exists()) {
            file.delete();
        }
        try {
            awsS3Service.downloadLargeFileToMemory(s3Path, S3_MMDB_FILE_TAG).map(inputStream -> {
                try (InputStream is = inputStream;
                    FileOutputStream fos = new FileOutputStream(file)) {
                    is.transferTo(fos);
                } catch (IOException e) {
                    log.error("save mmdb file from s3 fail, fallback to resource file", e);
                }
                return file;
            }).block();
        } catch (Exception e) {
            log.error("save mmdb file from s3 fail, fallback to resource file", e);
        }
        return file;
    }
}
