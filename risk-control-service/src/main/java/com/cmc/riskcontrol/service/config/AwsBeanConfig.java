package com.cmc.riskcontrol.service.config;

import com.cmc.framework.s3.CmcS3Client;
import com.cmc.riskcontrol.business.aws.AwsS3Config;
import com.cmc.riskcontrol.business.aws.AwsS3Service;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Configuration for AWS related beans.
 *
 * <AUTHOR>
 * @date 2021-04-19 15:07:18
 */
@Configuration
public class AwsBeanConfig {

    @Primary
    @Bean
    public AwsS3Service awsS3Service(AwsS3Config config, CmcS3Client cmcS3Client) {
        AwsS3Service s3Service = new AwsS3Service(config, cmcS3Client);
        return s3Service;
    }

    @Primary
    @Bean
    @ConfigurationProperties(prefix = "com.cmc.dquery-job.aws.s3")
    public AwsS3Config s3Config() {
        return new AwsS3Config();
    }

}
